// Telegram authentication utilities

import { validateTelegramInitData } from '../utils/validation';
import type { TelegramUser, TelegramInitData } from '../types';

export interface AuthResult {
	success: boolean;
	user?: TelegramUser;
	error?: string;
	isNewUser?: boolean;
	referrerCode?: string;
}

export class TelegramAuth {
	/**
	 * 验证 Telegram WebApp 初始化数据
	 */
	public static validateInitData(initData: string): boolean {
		return validateTelegramInitData(initData);
	}

	/**
	 * 解析 Telegram 用户数据
	 */
	public static parseInitData(initData: TelegramInitData): AuthResult {
		try {
			if (!initData.user) {
				return {
					success: false,
					error: '无法获取用户信息'
				};
			}

			return {
				success: true,
				user: initData.user,
				isNewUser: initData.is_new_user,
				referrerCode: initData.referrer_code
			};
		} catch (error) {
			return {
				success: false,
				error: '解析用户数据失败'
			};
		}
	}

	/**
	 * 创建用户会话
	 */
	public static async createSession(
		user: TelegramUser,
		referrerCode?: string
	): Promise<{
		success: boolean;
		sessionToken?: string;
		user?: any;
		error?: string;
	}> {
		try {
			console.log('🔄 调用认证API...');
			// 这里应该调用你的后端 API 来创建会话
			const response = await fetch('/api/auth/telegram', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					telegramUser: user,
					referrerCode
				})
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error('❌ API响应错误:', response.status, errorText);
				throw new Error(`创建会话失败: ${response.status}`);
			}

			const data = await response.json();
			console.log('✅ API响应成功:', {
				hasSessionToken: !!data.sessionToken,
				hasUser: !!data.user
			});

			return {
				success: true,
				sessionToken: data.sessionToken,
				user: data.user
			};
		} catch (error) {
			console.error('❌ 创建会话异常:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : '未知错误'
			};
		}
	}

	/**
	 * 验证会话令牌
	 */
	public static async validateSession(sessionToken: string): Promise<{
		success: boolean;
		user?: any;
		error?: string;
	}> {
		try {
			const response = await fetch('/api/auth/validate', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${sessionToken}`
				}
			});

			if (!response.ok) {
				throw new Error('会话验证失败');
			}

			const data = await response.json();

			return {
				success: true,
				user: data.user
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : '会话验证失败'
			};
		}
	}

	/**
	 * 登出
	 */
	public static async logout(): Promise<void> {
		try {
			await fetch('/api/auth/logout', {
				method: 'POST'
			});
		} catch (error) {
			console.error('登出失败:', error);
		}

		// 清除本地存储的会话信息
		if (typeof window !== 'undefined') {
			localStorage.removeItem('sessionToken');
			localStorage.removeItem('user');
		}
	}

	/**
	 * 获取存储的会话令牌
	 */
	public static getStoredSessionToken(): string | null {
		if (typeof window === 'undefined') return null;
		return localStorage.getItem('sessionToken');
	}

	/**
	 * 存储会话令牌
	 */
	public static storeSessionToken(token: string): void {
		if (typeof window !== 'undefined') {
			localStorage.setItem('sessionToken', token);
		}
	}

	/**
	 * 获取存储的用户信息
	 */
	public static getStoredUser(): any | null {
		if (typeof window === 'undefined') return null;

		const userStr = localStorage.getItem('user');
		if (!userStr) return null;

		try {
			return JSON.parse(userStr);
		} catch {
			return null;
		}
	}

	/**
	 * 存储用户信息
	 */
	public static storeUser(user: any): void {
		if (typeof window !== 'undefined') {
			localStorage.setItem('user', JSON.stringify(user));
		}
	}

	/**
	 * 清除存储的认证信息
	 */
	public static clearStoredAuth(): void {
		if (typeof window !== 'undefined') {
			localStorage.removeItem('sessionToken');
			localStorage.removeItem('user');
		}
	}
}

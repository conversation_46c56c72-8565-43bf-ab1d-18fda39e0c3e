<script lang="ts">
	import { toastStore, uiActions } from '$lib/stores';
	import { fromStore, get } from 'svelte/store';
	import { fly } from 'svelte/transition';

	let toasts = $derived($toastStore);
	function getToastIcon(type: string) {
		switch (type) {
			case 'success':
				return '✅';
			case 'error':
				return '❌';
			case 'warning':
				return '⚠️';
			case 'info':
				return 'ℹ️';
			default:
				return '📢';
		}
	}

	function getToastColors(type: string) {
		switch (type) {
			case 'success':
				return 'bg-green-50 border-green-200 text-green-800';
			case 'error':
				return 'bg-red-50 border-red-200 text-red-800';
			case 'warning':
				return 'bg-yellow-50 border-yellow-200 text-yellow-800';
			case 'info':
				return 'bg-blue-50 border-blue-200 text-blue-800';
			default:
				return 'bg-gray-50 border-gray-200 text-gray-800';
		}
	}
</script>

<!-- Toast Container -->
<div class="fixed top-4 right-4 z-50 w-full max-w-sm space-y-2">
	{#each toasts as toast (toast.id)}
		<div
			transition:fly={{ x: 300, duration: 300 }}
			class="rounded-lg border p-4 shadow-lg {getToastColors(toast.type)}"
		>
			<div class="flex items-start">
				<div class="mr-3 flex-shrink-0 text-lg">
					{getToastIcon(toast.type)}
				</div>

				<div class="min-w-0 flex-1">
					{#if toast.title}
						<h4 class="mb-1 text-sm font-medium">
							{toast.title}
						</h4>
					{/if}

					<p class="text-sm">
						{toast.message}
					</p>

					{#if toast.action}
						<button
							onclick={toast.action.handler}
							class="mt-2 text-sm font-medium underline hover:no-underline"
						>
							{toast.action.label}
						</button>
					{/if}
				</div>

				<button
					onclick={() => uiActions.toast.remove(toast.id)}
					class="ml-2 flex-shrink-0 text-gray-400 hover:text-gray-600"
				>
					<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						></path>
					</svg>
				</button>
			</div>
		</div>
	{/each}
</div>

<style>
	/* 确保在移动设备上正确显示 */
	@media (max-width: 640px) {
		.fixed.top-4.right-4 {
			top: 1rem;
			right: 1rem;
			left: 1rem;
			max-width: none;
		}
	}
</style>

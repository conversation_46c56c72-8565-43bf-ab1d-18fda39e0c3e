<script lang="ts">
	import { onMount } from 'svelte';
	import { isAuthenticated, userProfile } from '$lib/stores';
	import { formatPoints } from '$lib/utils/formatting';
	import { APP_CONFIG } from '$lib/utils/constants';

	let user = $derived($userProfile);

	let tasks = $state([
		{
			id: 'daily_checkin',
			title: '每日签到',
			description: '每天登录获得积分奖励',
			points: APP_CONFIG.points.daily_check_in,
			completed: false,
			type: 'daily'
		},
		{
			id: 'complete_profile',
			title: '完善资料',
			description: '完善个人资料获得一次性奖励',
			points: APP_CONFIG.points.profile_completion_bonus,
			completed: false,
			type: 'onetime'
		},
		{
			id: 'invite_friend',
			title: '邀请朋友',
			description: '邀请朋友注册获得积分奖励',
			points: APP_CONFIG.points.invite_bonus,
			completed: false,
			type: 'repeatable'
		}
	]);

	let pointHistory = $state([]);

	onMount(() => {
		loadTasks();
		loadPointHistory();
	});

	async function loadTasks() {
		// 加载任务状态
	}

	async function loadPointHistory() {
		// 加载积分历史
	}

	async function completeTask(taskId: string) {
		// 完成任务逻辑
		console.log('Completing task:', taskId);
	}

	function getTaskIcon(type: string) {
		switch (type) {
			case 'daily':
				return '📅';
			case 'onetime':
				return '🎯';
			case 'repeatable':
				return '🔄';
			default:
				return '✨';
		}
	}
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
	{#if !$isAuthenticated}
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<div class="mb-4 text-4xl">🔐</div>
				<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">请先登录</h2>
				<p class="text-gray-600 dark:text-gray-300">需要登录后才能查看成长任务</p>
			</div>
		</div>
	{:else}
		<!-- 页面头部 -->
		<header
			class="border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
		>
			<div class="px-4 py-4">
				<h1 class="text-xl font-bold text-gray-900 dark:text-white">成长</h1>
				<p class="mt-1 text-sm text-gray-600 dark:text-gray-300">完成任务获得积分奖励</p>
			</div>
		</header>

		<main class="space-y-6 p-4">
			<!-- 积分概览 -->
			{#if user}
				<div class="rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
					<div class="text-center">
						<div class="mb-2 text-3xl font-bold">
							{formatPoints(user.pointBalance)}
						</div>
						<div class="text-blue-100">当前积分</div>
					</div>

					<div class="mt-6 grid grid-cols-3 gap-4">
						<div class="text-center">
							<div class="text-lg font-semibold">
								{user.vipLevel}
							</div>
							<div class="text-sm text-blue-100">VIP 等级</div>
						</div>
						<div class="text-center">
							<div class="text-lg font-semibold">
								{user.trustScore}
							</div>
							<div class="text-sm text-blue-100">信誉分</div>
						</div>
						<div class="text-center">
							<div class="text-lg font-semibold">
								{user.profileCompletenessScore}%
							</div>
							<div class="text-sm text-blue-100">资料完整度</div>
						</div>
					</div>
				</div>
			{/if}

			<!-- 任务列表 -->
			<div class="space-y-4">
				<h2 class="text-lg font-semibold text-gray-900 dark:text-white">每日任务</h2>

				<div class="space-y-3">
					{#each tasks as task}
						<div
							class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
						>
							<div class="flex items-center justify-between">
								<div class="flex items-center space-x-3">
									<div class="text-2xl">
										{getTaskIcon(task.type)}
									</div>
									<div>
										<h3 class="font-medium text-gray-900 dark:text-white">
											{task.title}
										</h3>
										<p class="text-sm text-gray-600 dark:text-gray-300">
											{task.description}
										</p>
									</div>
								</div>

								<div class="text-right">
									{#if task.completed}
										<div class="font-medium text-green-500">已完成 ✓</div>
									{:else}
										<div class="space-y-2">
											<div class="font-medium text-blue-600 dark:text-blue-400">
												+{task.points} 积分
											</div>
											<button
												onclick={() => completeTask(task.id)}
												class="rounded bg-blue-500 px-3 py-1 text-sm text-white transition-colors hover:bg-blue-600"
											>
												完成
											</button>
										</div>
									{/if}
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- 积分历史 -->
			<div class="space-y-4">
				<h2 class="text-lg font-semibold text-gray-900 dark:text-white">积分历史</h2>

				{#if pointHistory.length === 0}
					<div
						class="rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-700 dark:bg-gray-800"
					>
						<div class="mb-4 text-4xl">📊</div>
						<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">暂无积分记录</h3>
						<p class="text-gray-600 dark:text-gray-300">完成任务后这里会显示积分变动记录</p>
					</div>
				{:else}
					<div class="space-y-2">
						{#each pointHistory as record}
							<!-- 积分记录项 -->
						{/each}
					</div>
				{/if}
			</div>

			<!-- Web3 集成预留 -->
			<div class="space-y-4">
				<h2 class="text-lg font-semibold text-gray-900 dark:text-white">Web3 任务</h2>

				<div
					class="rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-700 dark:bg-gray-800"
				>
					<div class="mb-4 text-4xl">🚀</div>
					<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">即将推出</h3>
					<p class="text-gray-600 dark:text-gray-300">TON 链集成和 Web3 任务正在开发中</p>
				</div>
			</div>
		</main>
	{/if}
</div>

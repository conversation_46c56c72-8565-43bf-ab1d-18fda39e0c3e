// Telegram data parser for different formats

export interface ParsedTelegramData {
	user: {
		id: number;
		first_name: string;
		last_name?: string;
		username?: string;
		language_code?: string;
		allows_write_to_pm?: boolean;
		photo_url?: string;
	};
	chat_instance?: string;
	chat_type?: string;
	auth_date?: number;
	signature?: string;
	hash?: string;
	start_param?: string;
}

/**
 * 解析您提供的Telegram数据格式
 * 格式: user=%7B%22id%22%3A6662549305...&chat_instance=...&auth_date=...
 */
export function parseTelegramWebData(rawData: string): ParsedTelegramData | null {
	try {
		const params = new URLSearchParams(rawData);

		// 解析用户数据
		const userStr = params.get('user');
		if (!userStr) {
			console.warn('No user data found in Telegram data');
			return null;
		}

		const user = JSON.parse(decodeURIComponent(userStr));

		// 构建完整的数据对象
		const data: ParsedTelegramData = {
			user,
			chat_instance: params.get('chat_instance') || undefined,
			chat_type: params.get('chat_type') || undefined,
			auth_date: params.get('auth_date') ? parseInt(params.get('auth_date')!) : undefined,
			signature: params.get('signature') || undefined,
			hash: params.get('hash') || undefined,
			start_param: params.get('start_param') || undefined
		};

		console.log('✅ Successfully parsed Telegram data:', data);
		return data;
	} catch (error) {
		console.error('❌ Failed to parse Telegram data:', error);
		return null;
	}
}

/**
 * 将解析的数据注入到sessionStorage中，供应用使用
 */
export function injectTelegramData(rawData: string): boolean {
	try {
		const parsed = parseTelegramWebData(rawData);
		if (!parsed) {
			return false;
		}

		// 将数据存储到sessionStorage
		sessionStorage.setItem('telegram-web-app-data', rawData);

		// 也创建一个格式化的版本
		sessionStorage.setItem('telegram-parsed-data', JSON.stringify(parsed));

		console.log('✅ Telegram data injected to sessionStorage');
		return true;
	} catch (error) {
		console.error('❌ Failed to inject Telegram data:', error);
		return false;
	}
}

/**
 * 从各种可能的来源获取Telegram数据
 */
export function getTelegramDataFromAnySource(): ParsedTelegramData | null {
	// 1. 尝试从Telegram WebApp API获取
	if (typeof window !== 'undefined' && window.Telegram?.WebApp?.initDataUnsafe?.user) {
		const webAppData = window.Telegram.WebApp.initDataUnsafe;
		return {
			user: webAppData.user,
			chat_instance: webAppData.chat_instance || undefined,
			chat_type: webAppData.chat_type || undefined,
			auth_date: webAppData.auth_date || Math.floor(Date.now() / 1000),
			hash: webAppData.hash || ''
		};
	}

	// 2. 尝试从sessionStorage获取
	if (typeof window !== 'undefined') {
		const possibleKeys = ['telegram-web-app-data', 'telegram-parsed-data', 'tgWebAppData'];

		for (const key of possibleKeys) {
			try {
				const data = sessionStorage.getItem(key);
				if (data) {
					if (key === 'telegram-parsed-data') {
						return JSON.parse(data);
					} else {
						const parsed = parseTelegramWebData(data);
						if (parsed) return parsed;
					}
				}
			} catch (error) {
				console.warn(`Failed to parse data from ${key}:`, error);
			}
		}
	}

	// 3. 尝试从URL参数获取
	if (typeof window !== 'undefined') {
		const urlParams = new URLSearchParams(window.location.search);
		const tgData = urlParams.get('tgWebAppData');
		if (tgData) {
			return parseTelegramWebData(decodeURIComponent(tgData));
		}
	}

	// 4. 尝试从URL hash获取
	if (typeof window !== 'undefined' && window.location.hash) {
		const hash = window.location.hash.substring(1);
		if (hash.includes('user=')) {
			return parseTelegramWebData(hash);
		}
	}

	return null;
}

/**
 * 验证Telegram数据的有效性
 */
export function validateTelegramData(data: ParsedTelegramData): boolean {
	if (!data.user || !data.user.id || !data.user.first_name) {
		console.warn('Invalid Telegram data: missing required user fields');
		return false;
	}

	// 检查auth_date是否在合理范围内（24小时内）
	if (data.auth_date) {
		const authTime = data.auth_date * 1000; // 转换为毫秒
		const now = Date.now();
		const maxAge = 24 * 60 * 60 * 1000; // 24小时

		if (now - authTime > maxAge) {
			console.warn('Telegram data is too old (>24 hours)');
			return false;
		}
	}

	return true;
}

/**
 * 开发环境下的调试助手
 */
export function debugTelegramData(): void {
	if (typeof window === 'undefined') return;

	console.group('🔍 Telegram Data Debug');

	// 检查所有可能的数据源
	const sources = [
		{
			name: 'Telegram WebApp API',
			data: window.Telegram?.WebApp?.initDataUnsafe
		},
		{
			name: 'SessionStorage (telegram-web-app-data)',
			data: sessionStorage.getItem('telegram-web-app-data')
		},
		{
			name: 'SessionStorage (telegram-parsed-data)',
			data: sessionStorage.getItem('telegram-parsed-data')
		},
		{
			name: 'URL Search Params',
			data: new URLSearchParams(window.location.search).get('tgWebAppData')
		},
		{
			name: 'URL Hash',
			data: window.location.hash
		}
	];

	sources.forEach((source) => {
		console.log(`${source.name}:`, source.data || 'Not found');
	});

	// 尝试获取最终数据
	const finalData = getTelegramDataFromAnySource();
	console.log('Final parsed data:', finalData);

	if (finalData) {
		console.log('Data validation:', validateTelegramData(finalData) ? '✅ Valid' : '❌ Invalid');
	}

	console.groupEnd();
}

// 如果在开发环境且有您提供的数据格式，自动注入
if (
	typeof window !== 'undefined' &&
	(window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')
) {
	// 检查是否已有数据
	const existingData = getTelegramDataFromAnySource();

	if (!existingData) {
		// 使用您提供的示例数据
		const sampleData =
			'user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22Sher%22%2C%22last_name%22%3A%22Locked%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1750668363&signature=Rj5w3NzMUu93NJ6CCqZWoQ45WdNY9eqYcastZ_JEjKheFVDZpfimbaqnPVUONCP1oOxGyMKXBoG0HrsvsHphDA&hash=81ac3f3d0dc817c6b3a87b31505be0867a27b9f9f6e67b9bd64b461dbcfee434';

		if (injectTelegramData(sampleData)) {
			console.log('🚀 Development: Injected sample Telegram data');
		}
	}
}

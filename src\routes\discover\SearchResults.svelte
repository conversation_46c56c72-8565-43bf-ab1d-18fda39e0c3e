<script lang="ts">
	import { onMount } from 'svelte';
	import { searchResults, isSearching, searchActions, searchOptions } from '$lib/stores';
	import { formatAge, formatDistance, formatLastActive } from '$lib/utils/formatting';
	import UserCard from './UserCard.svelte';

	interface Props {
		searchMode: 'basic' | 'advanced' | 'super';
	}

	let { searchMode }: Props = $props();

	let results = $derived($searchResults);
	let loading = $derived($isSearching);
	let options = $derived($searchOptions);

	let searchContainer: HTMLElement;
	let hasInitialSearch = $state(false);

	onMount(() => {
		// 执行初始搜索
		performSearch();
	});

	async function performSearch() {
		if (loading) return;

		const searchOptions = {
			mode: searchMode,
			filters: {},
			sortBy: 'relevance' as const,
			limit: 20,
			offset: 0
		};

		await searchActions.search(searchOptions);
		hasInitialSearch = true;
	}

	async function loadMore() {
		if (loading) return;
		await searchActions.loadMore();
	}

	function handleScroll() {
		if (!searchContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = searchContainer;
		const threshold = 100; // 距离底部100px时加载更多

		if (scrollHeight - scrollTop - clientHeight < threshold) {
			loadMore();
		}
	}

	// 监听搜索模式变化
	$effect(() => {
		if (hasInitialSearch && searchMode) {
			searchActions.clearResults();
			performSearch(); // 触发新搜索
		}
	});
</script>

<div bind:this={searchContainer} onscroll={handleScroll} class="h-full overflow-y-auto px-4 py-4">
	{#if !hasInitialSearch || (loading && results.length === 0)}
		<!-- 初始加载状态 -->
		<div class="flex h-64 items-center justify-center">
			<div class="text-center">
				<div
					class="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"
				></div>
				<p class="text-gray-600 dark:text-gray-300">正在搜索...</p>
			</div>
		</div>
	{:else if results.length === 0}
		<!-- 无结果状态 -->
		<div class="flex h-64 items-center justify-center">
			<div class="text-center">
				<div class="mb-4 text-4xl">🔍</div>
				<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">没有找到匹配的用户</h3>
				<p class="mb-4 text-gray-600 dark:text-gray-300">尝试调整搜索条件或过滤器</p>
				<button
					onclick={performSearch}
					class="rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
				>
					重新搜索
				</button>
			</div>
		</div>
	{:else}
		<!-- 搜索结果 -->
		<div class="space-y-4">
			<!-- 结果统计 -->
			<div class="flex items-center justify-between">
				<p class="text-sm text-gray-600 dark:text-gray-300">
					找到 {results.length} 个匹配用户
				</p>
				<button
					onclick={performSearch}
					class="text-sm font-medium text-blue-500 hover:text-blue-600"
				>
					刷新
				</button>
			</div>

			<!-- 用户卡片列表 -->
			<div class="grid grid-cols-1 gap-4">
				{#each results as result, index (result.user.id)}
					<UserCard
						user={result.user}
						matchScore={result.matchScore}
						distance={result.distance}
						commonKinks={result.commonKinks}
						isOnline={result.isOnline}
						{searchMode}
					/>
				{/each}
			</div>

			<!-- 加载更多指示器 -->
			{#if loading}
				<div class="flex items-center justify-center py-8">
					<div class="mr-3 h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
					<span class="text-gray-600 dark:text-gray-300">加载更多...</span>
				</div>
			{/if}

			<!-- 到底提示 -->
			{#if results.length > 0 && !loading}
				<div class="py-8 text-center">
					<p class="text-sm text-gray-500 dark:text-gray-400">已显示所有结果</p>
				</div>
			{/if}
		</div>
	{/if}
</div>

<!-- <style>
	/* 优化移动端滚动 */
	div[bind:this] {
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}
</style> -->

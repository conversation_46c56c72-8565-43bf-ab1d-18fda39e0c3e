<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { uiActions } from '$lib/stores';
	import { Search, Heart, Star, User } from '@lucide/svelte';
	import { cn } from '$lib/utils.js';
	import type { TabId } from '$lib/types';

	const TABS = [
		{
			id: 'discover',
			label: '发现',
			icon: Search,
			path: '/discover',
			requiresAuth: true,
			requiresBasicProfile: true
		},
		{
			id: 'interactions',
			label: '互动',
			icon: Heart,
			path: '/interactions',
			requiresAuth: true,
			requiresBasicProfile: true
		},
		{
			id: 'growth',
			label: '成长',
			icon: Star,
			path: '/growth',
			requiresAuth: true,
			requiresBasicProfile: false
		},
		{
			id: 'profile',
			label: '我的',
			icon: User,
			path: '/profile',
			requiresAuth: true,
			requiresBasicProfile: false
		}
	];

	// 获取当前路径对应的 tab
	let currentPath = $derived($page.url.pathname);
	let currentTab = $derived(TABS.find((tab) => currentPath.startsWith(tab.path))?.id || 'discover');

	// 同步 activeTab store
	$effect(() => {
		uiActions.tab.setActive(currentTab as TabId);
	});

	function navigateToTab(_tabId: string, path: string) {
		goto(path);
	}
</script>

<nav
	class="fixed right-0 bottom-0 left-0 z-50 border-t border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
>
	<div class="flex h-16 items-center justify-around px-2">
		{#each TABS as tab}
			{@const isActive = currentTab === tab.id}
			<button
				onclick={() => navigateToTab(tab.id, tab.path)}
				class="flex flex-1 flex-col items-center justify-center px-1 py-2 transition-colors duration-200 {isActive
					? 'text-blue-500'
					: 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
			>
				<svelte:component this={tab.icon} class="mb-1 h-5 w-5" />
				<span class="truncate text-xs font-medium">
					{tab.label}
				</span>

				{#if isActive}
					<div
						class="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-blue-500"
					></div>
				{/if}
			</button>
		{/each}
	</div>
</nav>

<style>
	/* 确保在 Telegram WebApp 中正确显示 */
	nav {
		-webkit-user-select: none;
		user-select: none;
		-webkit-tap-highlight-color: transparent;
	}

	button {
		-webkit-tap-highlight-color: transparent;
		touch-action: manipulation;
	}
</style>
